/* Excel Preview App Styles */

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
}

.container {
    max-width: 1200px;
}

.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.editable-cell {
    cursor: pointer;
    position: relative;
    padding: 4px;
    border-radius: 3px;
    transition: background-color 0.2s ease;
}

.editable-cell:hover {
    background-color: #e3f2fd;
    border: 1px solid #2196f3;
}

.editable-cell.editing {
    background-color: #fff3e0;
    border: 2px solid #ff9800;
}

.cell-value {
    display: block;
    min-height: 20px;
    word-wrap: break-word;
}

.cell-image {
    margin-top: 5px;
}

.cell-image img {
    border: 1px solid #ddd;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease;
}

.cell-image img:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.table {
    background-color: white;
    border-radius: 8px;
    overflow: hidden;
}

.table td {
    word-wrap: break-word;
    vertical-align: middle;
    padding: 2px 4px;
    border: none; /* Remove default borders - will be added by Excel formatting */
    position: relative;
    min-height: 20px;
    box-sizing: border-box;
    background-color: white; /* Default white background */
}

.table th {
    background: linear-gradient(to bottom, #f6f8fa 0%, #e9ecef 100%);
    font-weight: 600;
    border: 1px solid #d0d7de;
    padding: 4px 6px;
    text-align: center;
    color: #24292f;
    font-size: 12px;
}

.loading {
    display: none;
    color: #6c757d;
    font-style: italic;
}

.loading.show {
    display: block;
}

.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

.alert {
    border-radius: 8px;
}

.btn {
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.form-control, .form-select {
    border-radius: 6px;
    border: 1px solid #ced4da;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.form-control:focus, .form-select:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.table-responsive {
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .table td {
        max-width: 150px;
        font-size: 0.9rem;
    }
    
    .cell-image img {
        max-width: 80px !important;
        max-height: 80px !important;
    }
    
    .container {
        padding: 0 15px;
    }
}

/* Animation for successful updates */
.cell-updated {
    animation: cellUpdate 0.5s ease;
}

@keyframes cellUpdate {
    0% { background-color: #d4edda; }
    100% { background-color: transparent; }
}

/* Error state for cells */
.cell-error {
    background-color: #f8d7da;
    border-color: #dc3545;
}

/* Loading state for individual cells */
.cell-loading {
    position: relative;
    opacity: 0.6;
}

.cell-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 16px;
    height: 16px;
    margin: -8px 0 0 -8px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Cell editing styles */
.cell-edited {
    background-color: #fff3cd !important;
    border: 2px solid #ffc107 !important;
}

.cell-value[contenteditable="true"] {
    min-height: 20px;
    padding: 2px 4px;
    border-radius: 3px;
    outline: none;
    transition: background-color 0.2s ease;
}

.cell-value[contenteditable="true"]:focus {
    background-color: #e3f2fd;
    box-shadow: 0 0 0 2px #2196f3;
}

.cell-value[contenteditable="true"]:hover {
    background-color: #f5f5f5;
}

.edit-indicator {
    color: #ff9800;
    font-size: 12px;
    margin-left: 5px;
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.cell-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

/* Save button states */
#saveEditedBtn.btn-danger {
    animation: glow 2s infinite;
}

@keyframes glow {
    0% { box-shadow: 0 0 5px rgba(220, 53, 69, 0.5); }
    50% { box-shadow: 0 0 20px rgba(220, 53, 69, 0.8); }
    100% { box-shadow: 0 0 5px rgba(220, 53, 69, 0.5); }
}

/* Excel-like table styling */
.table {
    border-collapse: separate;
    border-spacing: 0;
    font-family: 'Calibri', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-size: 11px;
    line-height: 1.2;
    background-color: white;
}

/* Remove default borders - only show Excel-defined borders */
.table td {
    border: none;
}

/* Keep header borders */
.table th {
    border: 1px solid #d0d7de;
}

.table th:first-child {
    border-left: 1px solid #d0d7de;
}

.table thead th {
    border-top: 1px solid #d0d7de;
}

/* Override Bootstrap table styles for Excel-like appearance */
.table > :not(caption) > * > * {
    padding: 2px 4px;
    background-color: transparent;
    border-bottom-width: 1px;
}

/* Cell value styling to match Excel */
.cell-value {
    display: block;
    width: 100%;
    height: 100%;
    min-height: 16px;
    line-height: 1.2;
    word-wrap: break-word;
    overflow-wrap: break-word;
    white-space: pre-wrap;
}

/* Merged cell styling */
.table td[rowspan], .table td[colspan] {
    text-align: center;
    vertical-align: middle;
}

/* Excel-like grid lines */
.table-responsive {
    border: 2px solid #d0d7de;
    border-radius: 0;
    box-shadow: none;
}

/* Row and column headers */
.table th {
    background: linear-gradient(to bottom, #f6f8fa 0%, #e9ecef 100%);
    border-color: #d0d7de;
    user-select: none;
}

/* Hover effects for Excel-like interaction */
.table td:hover {
    background-color: rgba(0, 123, 255, 0.1) !important;
}

.table th:hover {
    background: linear-gradient(to bottom, #e9ecef 0%, #dee2e6 100%);
}
