﻿@page
@model IndexModel
@{
    ViewData["Title"] = "Excel File Preview - Web Forms Style";
}

@functions {
    public string GetCellStyle(ExcelPreviewApp_WebForms.Services.CellData cell)
    {
        return Model.GetCellStyle(cell);
    }

    public string GetCellValueStyle(ExcelPreviewApp_WebForms.Services.CellData cell)
    {
        return Model.GetCellValueStyle(cell);
    }
}

<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title mb-0">Excel File Preview - Web Forms Style</h3>
                </div>
                <div class="card-body">
                    @if (!string.IsNullOrEmpty(Model.ErrorMessage))
                    {
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            @Model.ErrorMessage
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    }

                    <form method="post" enctype="multipart/form-data">
                        @Html.AntiForgeryToken()
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="excelFile" class="form-label">Select Excel File:</label>
                                <input type="file" class="form-control" id="excelFile" asp-for="ExcelFile" accept=".xlsx,.xls" />
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">&nbsp;</label>
                                <button type="submit" name="action" value="upload" class="btn btn-primary d-block">
                                    <i class="fas fa-upload me-2"></i>Upload & Preview
                                </button>
                            </div>
                        </div>
                    </form>

                    @if (Model.SheetNames.Any())
                    {
                        <form method="post">
                            @Html.AntiForgeryToken()
                            <input type="hidden" name="ExcelFileBase64" value="@Model.ExcelFileBase64" />
                            <input type="hidden" name="ExcelFileName" value="@Model.ExcelFileName" />

                            <div class="row mb-3">
                                <div class="col-md-4">
                                    <label for="selectedSheet" class="form-label">Select Sheet:</label>
                                    <select class="form-select" id="selectedSheet" name="SelectedSheet" onchange="this.form.submit()">
                                        @foreach (var sheet in Model.SheetNames)
                                        {
                                            <option value="@sheet" selected="@(sheet == Model.SelectedSheet)">@sheet</option>
                                        }
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">&nbsp;</label>
                                    <button type="submit" name="action" value="download" class="btn btn-success d-block">
                                        <i class="fas fa-download me-2"></i>Download Original
                                    </button>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">&nbsp;</label>
                                    <button type="button" id="saveEditedBtn" class="btn btn-warning d-block">
                                        <i class="fas fa-save me-2"></i>Save Edited File
                                    </button>
                                </div>
                            </div>
                        </form>

                        <!-- Hidden form for saving edited data -->
                        <form id="saveEditedForm" method="post" style="display: none;">
                            @Html.AntiForgeryToken()
                            <input type="hidden" name="ExcelFileBase64" value="@Model.ExcelFileBase64" />
                            <input type="hidden" name="ExcelFileName" value="@Model.ExcelFileName" />
                            <input type="hidden" name="SelectedSheet" value="@Model.SelectedSheet" />
                            <input type="hidden" name="EditedCellsJson" id="editedCellsInput" />
                            <input type="hidden" name="action" value="save_edited" />
                        </form>

                        @if (Model.CellData.Any())
                        {
                            <div class="table-responsive">
                                <table class="table table-bordered table-sm">
                                    <thead>
                                        <tr>
                                            <th style="width: 50px; background-color: #f8f9fa; font-weight: bold;">#</th>
                                            @for (int col = 0; col < Model.CellData.First().Count; col++)
                                            {
                                                var firstCellInCol = Model.CellData.First()[col];
                                                var colWidth = firstCellInCol.ColumnWidth.HasValue && firstCellInCol.ColumnWidth > 0
                                                    ? $"{(int)(firstCellInCol.ColumnWidth.Value * 7)}px"
                                                    : "150px";
                                                <th style="width: @colWidth; background-color: #f8f9fa; font-weight: bold; text-align: center;">@((char)('A' + col))</th>
                                            }
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @for (int row = 0; row < Model.CellData.Count; row++)
                                        {
                                            <tr>
                                                @{
                                                    var firstCellInRow = Model.CellData[row].First();
                                                    var rowHeight = firstCellInRow.RowHeight.HasValue && firstCellInRow.RowHeight > 0
                                                        ? $"height: {(int)(firstCellInRow.RowHeight.Value * 1.33)}px;"
                                                        : "";
                                                }
                                                <td class="fw-bold" style="background-color: #f8f9fa; text-align: center; @rowHeight">@(row + 1)</td>
                                                @for (int col = 0; col < Model.CellData[row].Count; col++)
                                                {
                                                    var cell = Model.CellData[row][col];

                                                    // Skip cells that are part of a merged range but not the top-left cell
                                                    if (cell.IsPartOfMergedCell)
                                                    {
                                                        continue;
                                                    }

                                                    var cellStyle = GetCellStyle(cell);
                                                    var cellColWidth = cell.ColumnWidth.HasValue && cell.ColumnWidth > 0
                                                        ? $"width: {(int)(cell.ColumnWidth.Value * 7)}px;"
                                                        : "";
                                                    var cellRowHeight = cell.RowHeight.HasValue && cell.RowHeight > 0
                                                        ? $"height: {(int)(cell.RowHeight.Value * 1.33)}px;"
                                                        : "";

                                                    <td class="editable-cell"
                                                        data-address="@cell.Address"
                                                        data-sheet="@Model.SelectedSheet"
                                                        data-original-value="@cell.Value"
                                                        style="@cellStyle @cellColWidth @cellRowHeight"
                                                        @if (cell.HasMergedCells && cell.MergedRowSpan > 1) { <text>rowspan="@cell.MergedRowSpan"</text> }
                                                        @if (cell.HasMergedCells && cell.MergedColSpan > 1) { <text>colspan="@cell.MergedColSpan"</text> }>
                                                        <div class="cell-content">
                                                            <span class="cell-value" contenteditable="true" data-address="@cell.Address" style="@GetCellValueStyle(cell)">@cell.Value</span>
                                                            <span class="edit-indicator" style="display: none;">✏️</span>
                                                        </div>
                                                        @{
                                                            var hasImage = !string.IsNullOrEmpty(cell.ImageBase64);
                                                            var isTargetCell = cell.Address == "B1" || cell.Address == "L29";
                                                        }

                                                        @if (hasImage)
                                                        {
                                                            <div class="cell-image mt-1" style="background-color: yellow; border: 2px solid red; padding: 5px;">
                                                                <img src="data:@cell.ImageMimeType;base64,@cell.ImageBase64"
                                                                     alt="Excel Image (@cell.Address)"
                                                                     style="max-width: 150px; max-height: 150px; border: 1px solid blue;"
                                                                     class="img-thumbnail" />
                                                                <div style="font-size: 10px; color: red;">📷 Image in @cell.Address</div>
                                                            </div>
                                                        }

                                                        @if (isTargetCell)
                                                        {
                                                            <div style="background-color: lime; color: black; font-size: 10px; padding: 2px;">
                                                                🔍 Cell @cell.Address - HasImage: @hasImage - ImageBase64 Length: @(cell.ImageBase64?.Length ?? 0)
                                                            </div>
                                                        }
                                                    </td>
                                                }
                                            </tr>
                                        }
                                    </tbody>
                                </table>
                            </div>
                        }
                    }

                    <div class="loading mt-3">
                        <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                        Processing file...
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
