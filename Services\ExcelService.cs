using OfficeOpenXml;
using OfficeOpenXml.Drawing;
using ExcelPreviewApp.Models;
using System.Text;

namespace ExcelPreviewApp.Services
{
    public class ExcelService
    {
        public ExcelService()
        {
            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
        }

        public async Task<ExcelViewModel> ProcessExcelFile(IFormFile file, string selectedSheet = null)
        {
            var model = new ExcelViewModel();

            using (var stream = new MemoryStream())
            {
                await file.CopyToAsync(stream);
                // Store the file content for later use
                model.ExcelFileBase64 = Convert.ToBase64String(stream.ToArray());
                model.ExcelFileName = file.FileName;

                using (var package = new ExcelPackage(stream))
                {
                    var workbook = package.Workbook;
                    model.SheetNames = workbook.Worksheets.Select(s => s.Name).ToList();

                    var worksheet = string.IsNullOrEmpty(selectedSheet)
                        ? workbook.Worksheets[0]
                        : workbook.Worksheets[selectedSheet];

                    model.SelectedSheet = worksheet.Name;
                    var rowCount = Math.Min(worksheet.Dimension.End.Row, 50);
                    var colCount = worksheet.Dimension.End.Column;

                    // Get all drawings from the worksheet
                    var allDrawings = worksheet.Drawings.ToList();

                    // Process each row
                    for (int row = 1; row <= rowCount; row++)
                    {
                        var rowData = new List<CellData>();
                        // Process each column in the row
                        for (int col = 1; col <= colCount; col++)
                        {
                            var cell = worksheet.Cells[row, col];
                            var cellData = new CellData
                            {
                                Value = cell.Text,
                                Address = cell.Address,
                                ImageHtml = ""
                            };

                            // Only get images that are exactly in this cell
                            var drawings = allDrawings.Where(d =>
                                d.From.Row == row - 1 && // Exact row match
                                d.From.Column == col - 1 // Exact column match
                            ).ToList();

                            if (drawings.Any())
                            {
                                var imageHtml = new StringBuilder();
                                foreach (var drawing in drawings)
                                {
                                    if (drawing is ExcelPicture picture)
                                    {
                                        var imageBytes = picture.Image.ImageBytes;
                                        var base64Image = Convert.ToBase64String(imageBytes);
                                        var imageFormat = "png"; // Default to PNG format for embedded images
                                        imageHtml.Append($"<img src=\"data:image/{imageFormat};base64,{base64Image}\" style=\"max-width:100px; max-height:100px; margin:2px;\" />");
                                    }
                                }
                                cellData.ImageHtml = imageHtml.ToString();
                            }

                            rowData.Add(cellData);
                        }
                        model.GridData.Add(rowData);
                    }
                }
            }

            return model;
        }

        public async Task<(bool success, string newBase64)> UpdateCell(string fileBase64, string fileName, string sheetName, string cellAddress, string newValue)
        {
            try
            {
                var fileBytes = Convert.FromBase64String(fileBase64);
                using (var stream = new MemoryStream())
                {
                    stream.Write(fileBytes, 0, fileBytes.Length);
                    stream.Position = 0;

                    using (var package = new ExcelPackage(stream))
                    {
                        var worksheet = package.Workbook.Worksheets[sheetName];
                        var cell = worksheet.Cells[cellAddress];
                        cell.Value = newValue;

                        // Create a new stream for the modified content
                        using (var saveStream = new MemoryStream())
                        {
                            await package.SaveAsAsync(saveStream);
                            return (true, Convert.ToBase64String(saveStream.ToArray()));
                        }
                    }
                }
            }
            catch
            {
                return (false, null);
            }
        }

        public async Task<bool> SaveAsNewFile(string fileBase64, string newFilePath)
        {
            try
            {
                var fileBytes = Convert.FromBase64String(fileBase64);
                using (var stream = new MemoryStream(fileBytes))
                using (var package = new ExcelPackage(stream))
                {
                    var fileInfo = new FileInfo(newFilePath);
                    await package.SaveAsAsync(fileInfo);
                }
                return true;
            }
            catch
            {
                return false;
            }
        }

        public bool ValidateExcelFile(IFormFile file)
        {
            if (file == null || file.Length == 0)
                return false;

            var extension = Path.GetExtension(file.FileName).ToLower();
            var validExtensions = new[] { ".xlsx", ".xls" };

            if (!validExtensions.Contains(extension))
                return false;

            // 10MB max file size
            if (file.Length > 10 * 1024 * 1024)
                return false;

            return true;
        }
    }
}