using OfficeOpenXml;
using OfficeOpenXml.Drawing;
using OfficeOpenXml.Style;
using System.Text;
using System.Security.Cryptography;
using System.Text.Json;
using System.Drawing;

namespace ExcelPreviewApp_WebForms.Services
{
    public class ExcelService
    {
        public ExcelService()
        {
            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
        }

        public ExcelViewModel ProcessExcelFile(IFormFile file, string selectedSheet = null)
        {
            var model = new ExcelViewModel();

            using (var stream = new MemoryStream())
            {
                file.CopyTo(stream);
                // Store the file content for later use
                model.ExcelFileBase64 = Convert.ToBase64String(stream.ToArray());
                model.ExcelFileName = file.FileName;

                stream.Position = 0;
                using (var package = new ExcelPackage(stream))
                {
                    model.SheetNames = package.Workbook.Worksheets.Select(ws => ws.Name).ToList();

                    if (string.IsNullOrEmpty(selectedSheet))
                    {
                        selectedSheet = model.SheetNames.FirstOrDefault();
                    }

                    if (!string.IsNullOrEmpty(selectedSheet))
                    {
                        var worksheet = package.Workbook.Worksheets[selectedSheet];
                        if (worksheet != null)
                        {
                            model.SelectedSheet = selectedSheet;
                            model.CellData = ExtractCellData(worksheet);
                        }
                    }
                }
            }

            return model;
        }

        public ExcelViewModel ProcessExcelFromBase64(string base64Content, string fileName, string selectedSheet = null)
        {
            var model = new ExcelViewModel();
            var fileBytes = Convert.FromBase64String(base64Content);

            model.ExcelFileBase64 = base64Content;
            model.ExcelFileName = fileName;

            using (var stream = new MemoryStream(fileBytes))
            {
                using (var package = new ExcelPackage(stream))
                {
                    model.SheetNames = package.Workbook.Worksheets.Select(ws => ws.Name).ToList();

                    if (string.IsNullOrEmpty(selectedSheet))
                    {
                        selectedSheet = model.SheetNames.FirstOrDefault();
                    }

                    if (!string.IsNullOrEmpty(selectedSheet))
                    {
                        var worksheet = package.Workbook.Worksheets[selectedSheet];
                        if (worksheet != null)
                        {
                            model.SelectedSheet = selectedSheet;
                            model.CellData = ExtractCellData(worksheet);
                        }
                    }
                }
            }

            return model;
        }

        private List<List<CellData>> ExtractCellData(ExcelWorksheet worksheet)
        {
            var cellData = new List<List<CellData>>();
            var processedImages = new HashSet<string>(); // Track processed images by hash

            // Get the actual used range
            var startRow = worksheet.Dimension?.Start.Row ?? 1;
            var endRow = worksheet.Dimension?.End.Row ?? 1;
            var startCol = worksheet.Dimension?.Start.Column ?? 1;
            var endCol = worksheet.Dimension?.End.Column ?? 1;

            Console.WriteLine($"Original dimension range: Row {startRow}-{endRow}, Col {startCol}-{endCol}");

            // Ensure we include cells with images even if they're outside the used range
            startRow = Math.Min(startRow, 1); // Always start from row 1
            startCol = Math.Min(startCol, 1); // Always start from column 1
            endRow = Math.Max(endRow, 29);    // Ensure we include row 29 (where L29 is)
            endCol = Math.Max(endCol, 12);    // Ensure we include column 12 (where L is)

            Console.WriteLine($"Expanded dimension range: Row {startRow}-{endRow}, Col {startCol}-{endCol}");

            // Limit to reasonable size for display
            endRow = Math.Min(endRow, 100);
            endCol = Math.Min(endCol, 20);

            // Track merged cells to avoid duplicates
            var mergedCellsProcessed = new HashSet<string>();

            for (int row = startRow; row <= endRow; row++)
            {
                var rowData = new List<CellData>();
                for (int col = startCol; col <= endCol; col++)
                {
                    var cell = worksheet.Cells[row, col];
                    var cellInfo = new CellData
                    {
                        Address = cell.Address,
                        Value = cell.Value?.ToString() ?? "",
                        Row = row,
                        Column = col
                    };

                    // Check for merged cells
                    var mergedCell = worksheet.MergedCells.FirstOrDefault(mc =>
                    {
                        var range = worksheet.Cells[mc];
                        return range.Start.Row <= row &&
                               range.End.Row >= row &&
                               range.Start.Column <= col &&
                               range.End.Column >= col;
                    });

                    if (mergedCell != null)
                    {
                        var mergedRange = worksheet.Cells[mergedCell];

                        // Only process the top-left cell of merged range
                        if (row == mergedRange.Start.Row && col == mergedRange.Start.Column)
                        {
                            cellInfo.HasMergedCells = true;
                            cellInfo.MergedRowSpan = mergedRange.End.Row - mergedRange.Start.Row + 1;
                            cellInfo.MergedColSpan = mergedRange.End.Column - mergedRange.Start.Column + 1;
                            mergedCellsProcessed.Add(mergedCell);
                        }
                        else
                        {
                            // Skip cells that are part of a merged range but not the top-left
                            cellInfo.Value = "";
                            cellInfo.IsPartOfMergedCell = true;
                        }
                    }

                    // Extract formatting information
                    ExtractCellFormatting(cell, cellInfo, worksheet);

                    // Extract column width and row height
                    cellInfo.ColumnWidth = worksheet.Column(col).Width;
                    cellInfo.RowHeight = worksheet.Row(row).Height;

                    rowData.Add(cellInfo);
                }
                cellData.Add(rowData);
            }

            // Process images separately to avoid duplication
            Console.WriteLine($"Found {worksheet.Drawings.Count} drawings in worksheet");
            if (worksheet.Drawings.Any())
            {
                foreach (var drawing in worksheet.Drawings)
                {
                    Console.WriteLine($"Processing drawing: {drawing.Name}, Type: {drawing.GetType().Name}");

                    // Handle regular pictures
                    if (drawing is ExcelPicture picture)
                    {
                        try
                        {
                            // Check if the picture has valid image data
                            if (picture.Image == null)
                            {
                                Console.WriteLine("Picture.Image is null");
                                continue;
                            }

                            var imageBytes = picture.Image.ImageBytes;
                            Console.WriteLine($"Image bytes length: {imageBytes?.Length ?? 0}");

                            if (imageBytes != null && imageBytes.Length > 0)
                            {
                                // Create a hash of the image to detect duplicates
                                var imageHash = Convert.ToBase64String(SHA256.HashData(imageBytes));

                                // Only process if we haven't seen this image before
                                if (!processedImages.Contains(imageHash))
                                {
                                    processedImages.Add(imageHash);

                                    var base64Image = Convert.ToBase64String(imageBytes);
                                    var mimeType = GetMimeType(picture.Image.Type);
                                    Console.WriteLine($"Image type: {picture.Image.Type}, MIME type: {mimeType}");

                                    // Check if the image has position information (From property)
                                    if (picture.From != null)
                                    {
                                        // Find the cell that contains this image (use the top-left position)
                                        var imageRow = picture.From.Row + 1; // EPPlus uses 0-based indexing
                                        var imageCol = picture.From.Column + 1;
                                        Console.WriteLine($"Image position: Row {imageRow}, Col {imageCol}");

                                        // Make sure the image position is within our data range
                                        if (imageRow >= startRow && imageRow <= endRow &&
                                            imageCol >= startCol && imageCol <= endCol)
                                        {
                                            var targetRowIndex = imageRow - startRow;
                                            var targetColIndex = imageCol - startCol;

                                            if (targetRowIndex < cellData.Count && targetColIndex < cellData[targetRowIndex].Count)
                                            {
                                                cellData[targetRowIndex][targetColIndex].ImageBase64 = base64Image;
                                                cellData[targetRowIndex][targetColIndex].ImageMimeType = mimeType;
                                                Console.WriteLine($"Successfully assigned image to cell {cellData[targetRowIndex][targetColIndex].Address}");
                                            }
                                            else
                                            {
                                                Console.WriteLine($"Target indices out of bounds: Row count {cellData.Count}, Col count {(cellData.Count > 0 ? cellData[0].Count : 0)}");
                                            }
                                        }
                                    }
                                    else
                                    {
                                        Console.WriteLine($"Image has no position information (floating image). Attempting to place in logical position.");

                                        // For floating images, try to place them in a logical position
                                        if (cellData.Count > 0 && cellData[0].Count > 0)
                                        {
                                            bool imageAssigned = false;
                                            Console.WriteLine($"Attempting to place floating image. Grid size: {cellData.Count} x {cellData[0].Count}");

                                            // Strategy 1: Look for cells that might contain image-related text
                                            for (int r = 0; r < cellData.Count && !imageAssigned; r++)
                                            {
                                                for (int c = 0; c < cellData[r].Count && !imageAssigned; c++)
                                                {
                                                    var cellValue = cellData[r][c].Value?.ToLower() ?? "";
                                                    if (string.IsNullOrEmpty(cellData[r][c].ImageBase64) &&
                                                        (cellValue.Contains("logo") || cellValue.Contains("image") ||
                                                         cellValue.Contains("photo") || cellValue.Contains("picture") ||
                                                         string.IsNullOrEmpty(cellValue))) // Also try empty cells
                                                    {
                                                        cellData[r][c].ImageBase64 = base64Image;
                                                        cellData[r][c].ImageMimeType = mimeType;
                                                        imageAssigned = true;
                                                        Console.WriteLine($"Strategy 1: Assigned floating image to cell {cellData[r][c].Address} (value: '{cellValue}')");
                                                    }
                                                }
                                            }

                                            // Strategy 2: If not assigned, try the top-left area (common for logos)
                                            if (!imageAssigned)
                                            {
                                                Console.WriteLine("Strategy 1 failed, trying Strategy 2 (top-left area)");
                                                for (int r = 0; r < Math.Min(cellData.Count, 5) && !imageAssigned; r++)
                                                {
                                                    for (int c = 0; c < Math.Min(cellData[r].Count, 5) && !imageAssigned; c++)
                                                    {
                                                        if (string.IsNullOrEmpty(cellData[r][c].ImageBase64))
                                                        {
                                                            cellData[r][c].ImageBase64 = base64Image;
                                                            cellData[r][c].ImageMimeType = mimeType;
                                                            imageAssigned = true;
                                                            Console.WriteLine($"Strategy 2: Assigned floating image to cell {cellData[r][c].Address}");
                                                        }
                                                    }
                                                }
                                            }

                                            if (!imageAssigned)
                                            {
                                                Console.WriteLine("Both strategies failed to assign floating image");
                                            }

                                        }
                                    }
                                }

                            }
                            else
                            {
                                // Skip images with no data
                            }
                        }
                        catch (Exception ex)
                        {
                            // Log the error but continue processing
                            Console.WriteLine($"Error processing image: {ex.Message}");
                        }
                    }
                    // Handle group shapes that may contain pictures
                    else if (drawing is ExcelGroupShape groupShape)
                    {
                        Console.WriteLine($"Processing group shape: {groupShape.Name}");
                        ProcessGroupShape(groupShape, cellData, startRow, startCol, endRow, endCol);
                    }
                    else
                    {
                        Console.WriteLine($"Skipping unsupported drawing type: {drawing.GetType().Name}");
                    }
                }
            }

            return cellData;
        }

        private void ProcessGroupShape(ExcelGroupShape groupShape, List<List<CellData>> cellData, int startRow, int startCol, int endRow, int endCol)
        {
            try
            {
                // Recursively process all shapes in the group
                foreach (var shape in groupShape.Drawings)
                {
                    Console.WriteLine($"Processing shape in group: {shape.Name}, Type: {shape.GetType().Name}");

                    if (shape is ExcelPicture picture)
                    {
                        ProcessSinglePicture(picture, cellData, startRow, startCol, endRow, endCol);
                    }
                    else if (shape is ExcelGroupShape nestedGroup)
                    {
                        // Handle nested groups recursively
                        ProcessGroupShape(nestedGroup, cellData, startRow, startCol, endRow, endCol);
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error processing group shape {groupShape.Name}: {ex.Message}");
            }
        }

        private void ProcessSinglePicture(ExcelPicture picture, List<List<CellData>> cellData, int startRow, int startCol, int endRow, int endCol)
        {
            try
            {
                // Check if the picture has valid image data
                if (picture.Image == null)
                {
                    Console.WriteLine($"Picture.Image is null for {picture.Name}. Trying alternative access methods...");

                    // Try alternative methods to access image data for grouped images
                    try
                    {
                        // Method 1: Try to access through reflection to get internal image data
                        var imageProperty = picture.GetType().GetProperty("ImageBytes");
                        if (imageProperty != null)
                        {
                            var imageBytes = imageProperty.GetValue(picture) as byte[];
                            if (imageBytes != null && imageBytes.Length > 0)
                            {
                                Console.WriteLine($"Found image data via reflection: {imageBytes.Length} bytes");
                                ProcessImageBytes(imageBytes, picture.Name, cellData, startRow, startCol, endRow, endCol, picture.From);
                                return;
                            }
                        }

                        // Method 2: Try to access through other possible properties
                        var imageDataProperty = picture.GetType().GetProperty("ImageData");
                        if (imageDataProperty != null)
                        {
                            var imageData = imageDataProperty.GetValue(picture) as byte[];
                            if (imageData != null && imageData.Length > 0)
                            {
                                Console.WriteLine($"Found image data via ImageData property: {imageData.Length} bytes");
                                ProcessImageBytes(imageData, picture.Name, cellData, startRow, startCol, endRow, endCol, picture.From);
                                return;
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Error trying alternative access methods: {ex.Message}");
                    }

                    Console.WriteLine($"Could not access image data for {picture.Name}");
                    return;
                }

                var imageBytes = picture.Image.ImageBytes;
                if (imageBytes == null || imageBytes.Length == 0)
                {
                    Console.WriteLine("Image bytes are null or empty");
                    return;
                }

                Console.WriteLine($"Image bytes length: {imageBytes.Length}");
                ProcessImageBytes(imageBytes, picture.Name, cellData, startRow, startCol, endRow, endCol, picture.From, picture.Image.Type);


            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error processing picture {picture.Name}: {ex.Message}");
            }
        }

        private void ProcessImageBytes(byte[] imageBytes, string imageName, List<List<CellData>> cellData, int startRow, int startCol, int endRow, int endCol, ExcelPosition? position, ePictureType? imageType = null)
        {
            try
            {
                Console.WriteLine($"Processing image bytes for {imageName}: {imageBytes.Length} bytes");

                // Determine MIME type
                var mimeType = imageType switch
                {
                    ePictureType.Png => "image/png",
                    ePictureType.Jpg => "image/jpeg",
                    ePictureType.Gif => "image/gif",
                    ePictureType.Bmp => "image/bmp",
                    ePictureType.Tif => "image/tiff",
                    _ => DetectMimeTypeFromBytes(imageBytes) // Fallback to detection
                };

                Console.WriteLine($"Image type: {imageType}, MIME type: {mimeType}");

                var base64Image = Convert.ToBase64String(imageBytes);

                // Try to get position information
                bool imageAssigned = false;
                if (position != null)
                {
                    try
                    {
                        var imageRow = position.Row + 1; // Convert to 1-based
                        var imageCol = position.Column + 1; // Convert to 1-based

                        Console.WriteLine($"Image position: Row {imageRow}, Col {imageCol}");

                        // Make sure the image position is within our data range
                        if (imageRow >= startRow && imageRow <= endRow &&
                            imageCol >= startCol && imageCol <= endCol)
                        {
                            var targetRowIndex = imageRow - startRow;
                            var targetColIndex = imageCol - startCol;

                            if (targetRowIndex < cellData.Count && targetColIndex < cellData[targetRowIndex].Count)
                            {
                                cellData[targetRowIndex][targetColIndex].ImageBase64 = base64Image;
                                cellData[targetRowIndex][targetColIndex].ImageMimeType = mimeType;
                                imageAssigned = true;
                                Console.WriteLine($"Successfully assigned image to cell {cellData[targetRowIndex][targetColIndex].Address}");
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Error getting image position: {ex.Message}");
                    }
                }

                // If position-based assignment failed, try fallback strategies
                if (!imageAssigned)
                {
                    Console.WriteLine("Image has no position information (floating image). Attempting to place in logical position.");
                    Console.WriteLine($"Attempting to place floating image. Grid size: {cellData.Count} x {(cellData.Count > 0 ? cellData[0].Count : 0)}");

                    // Strategy 1: Look for cells that might contain image-related text or are empty
                    for (int r = 0; r < cellData.Count && !imageAssigned; r++)
                    {
                        for (int c = 0; c < cellData[r].Count && !imageAssigned; c++)
                        {
                            var cellValue = cellData[r][c].Value?.ToLower() ?? "";
                            if (string.IsNullOrEmpty(cellData[r][c].ImageBase64) &&
                                (cellValue.Contains("logo") || cellValue.Contains("image") ||
                                 cellValue.Contains("photo") || cellValue.Contains("picture") ||
                                 string.IsNullOrEmpty(cellValue))) // Also try empty cells
                            {
                                cellData[r][c].ImageBase64 = base64Image;
                                cellData[r][c].ImageMimeType = mimeType;
                                imageAssigned = true;
                                Console.WriteLine($"Strategy 1: Assigned floating image to cell {cellData[r][c].Address} (value: '{cellValue}')");
                            }
                        }
                    }

                    // Strategy 2: If not assigned, try the middle area (common for content images)
                    if (!imageAssigned)
                    {
                        Console.WriteLine("Strategy 1 failed, trying Strategy 2 (middle area)");
                        int midRow = cellData.Count / 2;
                        int midCol = cellData.Count > 0 ? cellData[0].Count / 2 : 0;

                        // Try a 3x3 area around the middle
                        for (int r = Math.Max(0, midRow - 1); r < Math.Min(cellData.Count, midRow + 2) && !imageAssigned; r++)
                        {
                            for (int c = Math.Max(0, midCol - 1); c < Math.Min(cellData[r].Count, midCol + 2) && !imageAssigned; c++)
                            {
                                if (string.IsNullOrEmpty(cellData[r][c].ImageBase64))
                                {
                                    cellData[r][c].ImageBase64 = base64Image;
                                    cellData[r][c].ImageMimeType = mimeType;
                                    imageAssigned = true;
                                    Console.WriteLine($"Strategy 2: Assigned floating image to cell {cellData[r][c].Address}");
                                }
                            }
                        }
                    }

                    if (!imageAssigned)
                    {
                        Console.WriteLine("Both strategies failed to assign floating image");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error processing image bytes for {imageName}: {ex.Message}");
            }
        }

        private string DetectMimeTypeFromBytes(byte[] imageBytes)
        {
            if (imageBytes.Length < 4) return "image/png"; // Default fallback

            // Check for common image file signatures
            if (imageBytes[0] == 0x89 && imageBytes[1] == 0x50 && imageBytes[2] == 0x4E && imageBytes[3] == 0x47)
                return "image/png";

            if (imageBytes[0] == 0xFF && imageBytes[1] == 0xD8 && imageBytes[2] == 0xFF)
                return "image/jpeg";

            if (imageBytes[0] == 0x47 && imageBytes[1] == 0x49 && imageBytes[2] == 0x46)
                return "image/gif";

            if (imageBytes[0] == 0x42 && imageBytes[1] == 0x4D)
                return "image/bmp";

            return "image/png"; // Default fallback
        }

        private void ExtractCellFormatting(ExcelRange cell, CellData cellInfo, ExcelWorksheet worksheet)
        {
            try
            {
                var style = cell.Style;

                // Background color - only set if explicitly defined
                if (style.Fill.PatternType == ExcelFillStyle.Solid)
                {
                    var bgColor = style.Fill.BackgroundColor;
                    if (!string.IsNullOrEmpty(bgColor.Rgb) && bgColor.Rgb.Length >= 8)
                    {
                        // Remove alpha channel and convert to hex
                        var hexColor = "#" + bgColor.Rgb.Substring(2);
                        // Only set if it's not white or transparent
                        if (hexColor.ToUpper() != "#FFFFFF" && hexColor.ToUpper() != "#000000")
                        {
                            cellInfo.BackgroundColor = hexColor;
                        }
                    }
                    else if (bgColor.Indexed > 0 && bgColor.Indexed != 64 && bgColor.Indexed != 65)
                    {
                        var indexedColor = GetIndexedColor(bgColor.Indexed);
                        if (indexedColor != "#FFFFFF" && indexedColor != "#000000")
                        {
                            cellInfo.BackgroundColor = indexedColor;
                        }
                    }
                }

                // Font properties
                var font = style.Font;
                cellInfo.FontFamily = font.Name ?? "Calibri";
                cellInfo.FontSize = font.Size > 0 ? font.Size : 11;
                cellInfo.IsBold = font.Bold;
                cellInfo.IsItalic = font.Italic;
                cellInfo.IsUnderline = font.UnderLine;

                // Font color - only set if explicitly defined and not default black
                if (!string.IsNullOrEmpty(font.Color.Rgb) && font.Color.Rgb.Length >= 8)
                {
                    var fontHex = "#" + font.Color.Rgb.Substring(2);
                    if (fontHex.ToUpper() != "#000000")
                    {
                        cellInfo.FontColor = fontHex;
                    }
                }
                else if (font.Color.Indexed > 0 && font.Color.Indexed != 64)
                {
                    var indexedColor = GetIndexedColor(font.Color.Indexed);
                    if (indexedColor != "#000000")
                    {
                        cellInfo.FontColor = indexedColor;
                    }
                }

                // Alignment
                cellInfo.HorizontalAlignment = style.HorizontalAlignment.ToString().ToLower();
                cellInfo.VerticalAlignment = style.VerticalAlignment.ToString().ToLower();

                // Borders - only extract if they have actual styles
                if (style.Border.Top.Style != ExcelBorderStyle.None)
                    cellInfo.BorderTop = GetBorderStyle(style.Border.Top);
                if (style.Border.Bottom.Style != ExcelBorderStyle.None)
                    cellInfo.BorderBottom = GetBorderStyle(style.Border.Bottom);
                if (style.Border.Left.Style != ExcelBorderStyle.None)
                    cellInfo.BorderLeft = GetBorderStyle(style.Border.Left);
                if (style.Border.Right.Style != ExcelBorderStyle.None)
                    cellInfo.BorderRight = GetBorderStyle(style.Border.Right);

                // Column width and row height
                cellInfo.ColumnWidth = worksheet.Column(cellInfo.Column).Width;
                cellInfo.RowHeight = worksheet.Row(cellInfo.Row).Height;

                // Check for merged cells
                var mergedCell = worksheet.MergedCells.FirstOrDefault(m =>
                    worksheet.Cells[m].Start.Row <= cellInfo.Row &&
                    worksheet.Cells[m].End.Row >= cellInfo.Row &&
                    worksheet.Cells[m].Start.Column <= cellInfo.Column &&
                    worksheet.Cells[m].End.Column >= cellInfo.Column);

                if (!string.IsNullOrEmpty(mergedCell))
                {
                    var mergedRange = worksheet.Cells[mergedCell];
                    cellInfo.HasMergedCells = true;
                    cellInfo.MergedRowSpan = mergedRange.End.Row - mergedRange.Start.Row + 1;
                    cellInfo.MergedColSpan = mergedRange.End.Column - mergedRange.Start.Column + 1;
                }
            }
            catch (Exception ex)
            {
                // Log error but continue processing
                Console.WriteLine($"Error extracting formatting for cell {cellInfo.Address}: {ex.Message}");
            }
        }

        private string GetIndexedColor(int colorIndex)
        {
            // Excel's standard color palette
            var indexedColors = new Dictionary<int, string>
            {
                { 0, "#000000" },   // Automatic/Black
                { 1, "#FFFFFF" },   // White
                { 2, "#FF0000" },   // Red
                { 3, "#00FF00" },   // Bright Green
                { 4, "#0000FF" },   // Blue
                { 5, "#FFFF00" },   // Yellow
                { 6, "#FF00FF" },   // Magenta
                { 7, "#00FFFF" },   // Cyan
                { 8, "#000000" },   // Black
                { 9, "#FFFFFF" },   // White
                { 10, "#FF0000" },  // Red
                { 11, "#00FF00" },  // Bright Green
                { 12, "#0000FF" },  // Blue
                { 13, "#FFFF00" },  // Yellow
                { 14, "#FF00FF" },  // Magenta
                { 15, "#00FFFF" },  // Cyan
                { 16, "#800000" },  // Dark Red
                { 17, "#008000" },  // Green
                { 18, "#000080" },  // Dark Blue
                { 19, "#808000" },  // Dark Yellow
                { 20, "#800080" },  // Dark Magenta
                { 21, "#008080" },  // Dark Cyan
                { 22, "#C0C0C0" },  // Light Gray
                { 23, "#808080" },  // Gray
                { 24, "#9999FF" },  // Light Blue
                { 25, "#993366" },  // Light Purple
                { 26, "#FFFFCC" },  // Light Yellow
                { 27, "#CCFFFF" },  // Light Cyan
                { 28, "#660066" },  // Dark Purple
                { 29, "#FF8080" },  // Light Red
                { 30, "#0066CC" },  // Medium Blue
                { 31, "#CCCCFF" },  // Very Light Blue
                { 32, "#000080" },  // Navy Blue
                { 33, "#FF00FF" },  // Bright Magenta
                { 34, "#FFFF00" },  // Bright Yellow
                { 35, "#00FFFF" },  // Bright Cyan
                { 36, "#800080" },  // Purple
                { 37, "#800000" },  // Maroon
                { 38, "#008080" },  // Teal
                { 39, "#0000FF" },  // Bright Blue
                { 40, "#00CCFF" },  // Sky Blue
                { 41, "#CCFFFF" },  // Light Turquoise
                { 42, "#CCFFCC" },  // Light Green
                { 43, "#FFFF99" },  // Light Yellow
                { 44, "#99CCFF" },  // Pale Blue
                { 45, "#FF99CC" },  // Pink
                { 46, "#CC99FF" },  // Lavender
                { 47, "#FFCC99" },  // Peach
                { 48, "#3366FF" },  // Bright Blue
                { 49, "#33CCCC" },  // Turquoise
                { 50, "#99CC00" },  // Lime
                { 51, "#FFCC00" },  // Gold
                { 52, "#FF9900" },  // Orange
                { 53, "#FF6600" },  // Red Orange
                { 54, "#666699" },  // Blue Gray
                { 55, "#969696" },  // Gray
                { 56, "#003366" },  // Dark Teal
                { 57, "#339966" },  // Sea Green
                { 58, "#003300" },  // Dark Green
                { 59, "#333300" },  // Olive Green
                { 60, "#993300" },  // Brown
                { 61, "#993366" },  // Plum
                { 62, "#333399" },  // Indigo
                { 63, "#333333" },  // Gray
                { 64, "#000000" },  // System foreground (usually black)
                { 65, "#FFFFFF" },  // System background (usually white)
            };

            return indexedColors.ContainsKey(colorIndex) ? indexedColors[colorIndex] : "";
        }

        private string GetBorderStyle(ExcelBorderItem border)
        {
            if (border.Style == ExcelBorderStyle.None)
                return "";

            var color = "#000000"; // Default black

            // Only extract color if it's explicitly set
            if (!string.IsNullOrEmpty(border.Color.Rgb) && border.Color.Rgb.Length >= 8)
            {
                color = "#" + border.Color.Rgb.Substring(2);
            }
            else if (border.Color.Indexed > 0 && border.Color.Indexed != 64)
            {
                var indexedColor = GetIndexedColor(border.Color.Indexed);
                if (!string.IsNullOrEmpty(indexedColor))
                {
                    color = indexedColor;
                }
            }

            var width = border.Style switch
            {
                ExcelBorderStyle.Thin => "1px",
                ExcelBorderStyle.Medium => "2px",
                ExcelBorderStyle.Thick => "3px",
                ExcelBorderStyle.Double => "3px",
                _ => "1px"
            };

            var style = border.Style switch
            {
                ExcelBorderStyle.Dashed => "dashed",
                ExcelBorderStyle.Dotted => "dotted",
                ExcelBorderStyle.Double => "double",
                _ => "solid"
            };

            return $"{width} {style} {color}";
        }

        private string GetMimeType(ePictureType? imageType)
        {
            return imageType switch
            {
                ePictureType.Jpg => "image/jpeg",
                ePictureType.Png => "image/png",
                ePictureType.Gif => "image/gif",
                ePictureType.Bmp => "image/bmp",
                ePictureType.Tif => "image/tiff",
                _ => "image/png"
            };
        }

        public byte[]? SaveEditedExcelFile(string base64Content, string fileName, string sheetName, string editedCellsJson)
        {
            try
            {
                var fileBytes = Convert.FromBase64String(base64Content);

                // Parse the edited cells JSON
                var editedCells = JsonSerializer.Deserialize<Dictionary<string, string>>(editedCellsJson);
                if (editedCells == null || !editedCells.Any())
                {
                    return null;
                }

                using (var stream = new MemoryStream(fileBytes))
                {
                    using (var package = new ExcelPackage(stream))
                    {
                        var worksheet = package.Workbook.Worksheets[sheetName];
                        if (worksheet == null)
                        {
                            return null;
                        }

                        // Apply the edited values to the worksheet
                        foreach (var edit in editedCells)
                        {
                            var cellAddress = edit.Key;
                            var newValue = edit.Value;

                            try
                            {
                                var cell = worksheet.Cells[cellAddress];
                                if (cell != null)
                                {
                                    // Try to parse as number first, then as text
                                    if (double.TryParse(newValue, out double numericValue))
                                    {
                                        cell.Value = numericValue;
                                    }
                                    else
                                    {
                                        cell.Value = newValue;
                                    }
                                }
                            }
                            catch (Exception ex)
                            {
                                // Log error but continue with other cells
                                Console.WriteLine($"Error updating cell {cellAddress}: {ex.Message}");
                            }
                        }

                        // Save the modified package to a new byte array
                        using (var outputStream = new MemoryStream())
                        {
                            package.SaveAs(outputStream);
                            return outputStream.ToArray();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error saving edited Excel file: {ex.Message}");
                return null;
            }
        }

        public bool ValidateExcelFile(IFormFile file)
        {
            if (file == null || file.Length == 0)
                return false;

            var extension = Path.GetExtension(file.FileName).ToLower();
            var validExtensions = new[] { ".xlsx", ".xls" };

            if (!validExtensions.Contains(extension))
                return false;

            // 10MB max file size
            if (file.Length > 10 * 1024 * 1024)
                return false;

            return true;
        }
    }

    public class ExcelViewModel
    {
        public IFormFile ExcelFile { get; set; }
        public string ExcelFileBase64 { get; set; } = "";
        public string ExcelFileName { get; set; } = "";
        public List<string> SheetNames { get; set; } = new List<string>();
        public string SelectedSheet { get; set; } = "";
        public List<List<CellData>> CellData { get; set; } = new List<List<CellData>>();
        public string ErrorMessage { get; set; } = "";
    }

    public class CellData
    {
        public string Address { get; set; } = "";
        public string Value { get; set; } = "";
        public int Row { get; set; }
        public int Column { get; set; }
        public string ImageBase64 { get; set; } = "";
        public string ImageMimeType { get; set; } = "";

        // Formatting properties
        public string BackgroundColor { get; set; } = "";
        public string FontColor { get; set; } = "";
        public string FontFamily { get; set; } = "";
        public double FontSize { get; set; } = 11;
        public bool IsBold { get; set; } = false;
        public bool IsItalic { get; set; } = false;
        public bool IsUnderline { get; set; } = false;
        public string HorizontalAlignment { get; set; } = "";
        public string VerticalAlignment { get; set; } = "";
        public string BorderTop { get; set; } = "";
        public string BorderBottom { get; set; } = "";
        public string BorderLeft { get; set; } = "";
        public string BorderRight { get; set; } = "";
        public double? ColumnWidth { get; set; }
        public double? RowHeight { get; set; }
        public bool HasMergedCells { get; set; } = false;
        public int MergedRowSpan { get; set; } = 1;
        public int MergedColSpan { get; set; } = 1;
        public bool IsPartOfMergedCell { get; set; } = false;
    }
}
