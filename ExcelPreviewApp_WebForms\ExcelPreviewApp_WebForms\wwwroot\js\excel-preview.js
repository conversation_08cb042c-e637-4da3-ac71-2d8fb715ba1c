// Excel Preview App JavaScript

$(document).ready(function () {
    initializeApp();
});

// Global object to track edited cells
var editedCells = {};

function initializeApp() {
    // Show loading spinner on form submit
    $('form').submit(function (e) {
        console.log('Form submitted');
        console.log('Form action:', $(this).find('button[type="submit"]:focus').val());

        var fileInput = $('#excelFile')[0];
        if (fileInput && fileInput.files && fileInput.files.length > 0) {
            console.log('File selected:', fileInput.files[0].name);
            console.log('File size:', fileInput.files[0].size);
        } else {
            console.log('No file selected');
        }

        if (validateForm()) {
            $('.loading').addClass('show');
            $('button[type="submit"]').prop('disabled', true);
        } else {
            console.log('Form validation failed');
            return false;
        }
    });

    // Handle image hover effects
    $(document).on('mouseenter', '.cell-image img', function () {
        $(this).css('cursor', 'pointer');
    });

    // Handle image click for larger view
    $(document).on('click', '.cell-image img', function () {
        showImageModal($(this).attr('src'));
    });

    // Initialize cell editing
    initializeCellEditing();

    // Initialize save edited file functionality
    initializeSaveEdited();
}

function showError(message) {
    // Create or update error alert
    var $errorAlert = $('.alert-danger');
    if ($errorAlert.length === 0) {
        $errorAlert = $('<div class="alert alert-danger alert-dismissible fade show mt-3" role="alert">' +
            '<span class="error-message"></span>' +
            '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>' +
            '</div>');
        $('.card-body').prepend($errorAlert);
    }
    
    $errorAlert.find('.error-message').text(message);
    $errorAlert.show();
    
    // Auto-hide after 5 seconds
    setTimeout(function () {
        $errorAlert.fadeOut();
    }, 5000);
}

function showImageModal(imageSrc) {
    // Create modal if it doesn't exist
    var modalId = 'imageModal';
    var $modal = $('#' + modalId);
    
    if ($modal.length === 0) {
        var modalHtml = `
            <div class="modal fade" id="${modalId}" tabindex="-1">
                <div class="modal-dialog modal-lg modal-dialog-centered">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">Image Preview</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body text-center">
                            <img src="" class="img-fluid" style="max-height: 70vh;" />
                        </div>
                    </div>
                </div>
            </div>
        `;
        $('body').append(modalHtml);
        $modal = $('#' + modalId);
    }
    
    // Set image source and show modal
    $modal.find('img').attr('src', imageSrc);
    var modal = new bootstrap.Modal($modal[0]);
    modal.show();
}

function escapeHtml(text) {
    var map = {
        '&': '&amp;',
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#039;'
    };
    
    return text.replace(/[&<>"']/g, function(m) { return map[m]; });
}

// Utility function to show loading state
function showLoading(show) {
    if (show) {
        $('.loading').addClass('show');
    } else {
        $('.loading').removeClass('show');
    }
}

// Handle form validation
function validateForm() {
    console.log('validateForm called');

    var fileInput = $('#excelFile')[0];
    if (!fileInput || !fileInput.files || fileInput.files.length === 0) {
        console.log('No file input or no files selected, allowing submission for other actions');
        return true; // Allow form submission for other actions
    }

    var file = fileInput.files[0];
    console.log('Validating file:', file.name, 'Size:', file.size);

    var validExtensions = ['.xlsx', '.xls'];
    var fileExtension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'));
    console.log('File extension:', fileExtension);

    if (validExtensions.indexOf(fileExtension) === -1) {
        console.log('Invalid file extension');
        showError('Please select a valid Excel file (.xlsx or .xls).');
        return false;
    }

    if (file.size > 10 * 1024 * 1024) { // 10MB
        console.log('File too large');
        showError('File size must be less than 10MB.');
        return false;
    }

    console.log('File validation passed');
    return true;
}

// Initialize cell editing functionality
function initializeCellEditing() {
    // Handle cell value changes
    $(document).on('input', '.cell-value[contenteditable="true"]', function () {
        var $cell = $(this);
        var address = $cell.data('address');
        var originalValue = $cell.closest('.editable-cell').data('original-value');
        var newValue = $cell.text().trim();

        // Track the change
        if (newValue !== originalValue) {
            editedCells[address] = newValue;
            $cell.siblings('.edit-indicator').show();
            $cell.closest('.editable-cell').addClass('cell-edited');
        } else {
            delete editedCells[address];
            $cell.siblings('.edit-indicator').hide();
            $cell.closest('.editable-cell').removeClass('cell-edited');
        }

        // Update save button state
        updateSaveButtonState();
    });

    // Handle Enter key to move to next cell
    $(document).on('keydown', '.cell-value[contenteditable="true"]', function (e) {
        if (e.key === 'Enter') {
            e.preventDefault();
            $(this).blur();
        }
    });
}

// Initialize save edited file functionality
function initializeSaveEdited() {
    $('#saveEditedBtn').click(function () {
        if (Object.keys(editedCells).length === 0) {
            showError('No changes to save. Please edit some cells first.');
            return;
        }

        // Prepare the edited cells data
        var editedCellsJson = JSON.stringify(editedCells);
        $('#editedCellsInput').val(editedCellsJson);

        // Submit the hidden form
        $('#saveEditedForm').submit();
    });
}

// Update save button state based on edited cells
function updateSaveButtonState() {
    var hasEdits = Object.keys(editedCells).length > 0;
    var $saveBtn = $('#saveEditedBtn');

    if (hasEdits) {
        $saveBtn.removeClass('btn-warning').addClass('btn-danger');
        $saveBtn.html('<i class="fas fa-save me-2"></i>Save Edited File (' + Object.keys(editedCells).length + ' changes)');
    } else {
        $saveBtn.removeClass('btn-danger').addClass('btn-warning');
        $saveBtn.html('<i class="fas fa-save me-2"></i>Save Edited File');
    }
}
