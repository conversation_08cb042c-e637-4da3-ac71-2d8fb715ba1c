using Microsoft.AspNetCore.Http;

namespace ExcelPreviewApp.Models
{
    public class ExcelViewModel
    {
        public IFormFile ExcelFile { get; set; }
        public string ExcelFileBase64 { get; set; }
        public string ExcelFileName { get; set; }
        public List<List<CellData>> GridData { get; set; } = new List<List<CellData>>();
        public string ErrorMessage { get; set; }
        public List<string> SheetNames { get; set; } = new List<string>();
        public string SelectedSheet { get; set; }
    }

    public class CellData
    {
        public string Value { get; set; }
        public string ImageHtml { get; set; }
        public string Address { get; set; }
        public bool IsEditable { get; set; } = true;
    }
}