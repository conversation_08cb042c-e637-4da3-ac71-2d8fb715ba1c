/* _content/ExcelPreviewApp_WebForms/Pages/Shared/_Layout.cshtml.rz.scp.css */
/* Please see documentation at https://learn.microsoft.com/aspnet/core/client-side/bundling-and-minification
for details on configuring this project to bundle and minify static web assets. */

a.navbar-brand[b-ruu4he5bw3] {
  white-space: normal;
  text-align: center;
  word-break: break-all;
}

a[b-ruu4he5bw3] {
  color: #0077cc;
}

.btn-primary[b-ruu4he5bw3] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.nav-pills .nav-link.active[b-ruu4he5bw3], .nav-pills .show > .nav-link[b-ruu4he5bw3] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.border-top[b-ruu4he5bw3] {
  border-top: 1px solid #e5e5e5;
}
.border-bottom[b-ruu4he5bw3] {
  border-bottom: 1px solid #e5e5e5;
}

.box-shadow[b-ruu4he5bw3] {
  box-shadow: 0 .25rem .75rem rgba(0, 0, 0, .05);
}

button.accept-policy[b-ruu4he5bw3] {
  font-size: 1rem;
  line-height: inherit;
}

.footer[b-ruu4he5bw3] {
  position: absolute;
  bottom: 0;
  width: 100%;
  white-space: nowrap;
  line-height: 60px;
}
