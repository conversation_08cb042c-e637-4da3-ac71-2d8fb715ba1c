using System.Diagnostics;
using Microsoft.AspNetCore.Mvc;
using ExcelPreviewApp.Models;
using ExcelPreviewApp.Services;

namespace ExcelPreviewApp.Controllers;

public class HomeController : Controller
{
    private readonly ILogger<HomeController> _logger;
    private readonly ExcelService _excelService;

    public HomeController(ILogger<HomeController> logger, ExcelService excelService)
    {
        _logger = logger;
        _excelService = excelService;
    }

    public IActionResult Index()
    {
        return View(new ExcelViewModel());
    }

    [HttpPost]
    public async Task<IActionResult> Index(ExcelViewModel model)
    {
        if (model.ExcelFile != null)
        {
            if (!_excelService.ValidateExcelFile(model.ExcelFile))
            {
                model.ErrorMessage = "Please upload a valid Excel file (xlsx/xls) under 10MB.";
                return View(model);
            }

            try
            {
                model = await _excelService.ProcessExcelFile(model.ExcelFile, model.SelectedSheet);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing Excel file");
                model.ErrorMessage = "Error processing the Excel file. Please try again.";
            }
        }

        return View(model);
    }

    [HttpPost]
    public async Task<IActionResult> ChangeSheet([FromForm] string selectedSheet, [FromForm] string excelFileBase64, [FromForm] string excelFileName)
    {
        var model = new ExcelViewModel
        {
            ExcelFileBase64 = excelFileBase64,
            ExcelFileName = excelFileName,
            SelectedSheet = selectedSheet
        };

        try
        {
            // Convert base64 back to IFormFile
            byte[] bytes = Convert.FromBase64String(excelFileBase64);
            var stream = new MemoryStream(bytes);
            IFormFile file = new FormFile(stream, 0, bytes.Length, "ExcelFile", excelFileName);
            model = await _excelService.ProcessExcelFile(file, selectedSheet);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing Excel file during sheet change");
            model.ErrorMessage = "Error processing the Excel file. Please try again.";
        }

        return View("Index", model);
    }

    [HttpPost]
    public async Task<IActionResult> UpdateCell([FromBody] CellUpdateModel updateModel)
    {
        try
        {
            var (success, newBase64) = await _excelService.UpdateCell(
                updateModel.FileBase64,
                updateModel.FileName,
                updateModel.SheetName,
                updateModel.CellAddress,
                updateModel.NewValue
            );

            return Json(new { success, newBase64 });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating cell");
            return Json(new { success = false, newBase64 = (string)null });
        }
    }

    [HttpPost]
    public async Task<IActionResult> SaveAs([FromBody] SaveAsModel model)
    {
        try
        {
            string newFilePath = Path.Combine(Path.GetTempPath(), $"Modified_{model.FileName}");
            var success = await _excelService.SaveAsNewFile(model.FileBase64, newFilePath);

            if (success)
            {
                // Return the file for download
                byte[] fileBytes = System.IO.File.ReadAllBytes(newFilePath);
                System.IO.File.Delete(newFilePath); // Clean up temp file
                return File(fileBytes, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", $"Modified_{model.FileName}");
            }

            return Json(new { success = false, message = "Failed to save the file" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error saving Excel file");
            return Json(new { success = false, message = "Error saving the file" });
        }
    }

    [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
    public IActionResult Error()
    {
        return View(new ErrorViewModel { RequestId = Activity.Current?.Id ?? HttpContext.TraceIdentifier });
    }
}

public class CellUpdateModel
{
    public string FileBase64 { get; set; }
    public string FileName { get; set; }
    public string SheetName { get; set; }
    public string CellAddress { get; set; }
    public string NewValue { get; set; }
}

public class SaveAsModel
{
    public string FileBase64 { get; set; }
    public string FileName { get; set; }
}
