@model ExcelViewModel

@{
    ViewData["Title"] = "Excel File Preview";
}

<div class="container mt-4">
    <h1 class="mb-4">Excel File Preview</h1>

    <div class="card">
        <div class="card-body">
            <form method="post" enctype="multipart/form-data" asp-action="Index">
                <div class="mb-3">
                    <label for="ExcelFile" class="form-label">Choose Excel File</label>
                    <input type="file" class="form-control" id="ExcelFile" name="ExcelFile" accept=".xlsx,.xls" />
                </div>
                <button type="submit" class="btn btn-primary">Upload & Preview</button>
            </form>

            @if (!string.IsNullOrEmpty(Model.ErrorMessage))
            {
                <div class="alert alert-danger mt-3" role="alert">
                    @Model.ErrorMessage
                </div>
            }

            @if (Model.GridData.Any())
            {
                <div class="mt-4">
                    @if (Model.SheetNames.Count > 1)
                    {
                        <form method="post" asp-action="ChangeSheet" class="mb-3">
                            <input type="hidden" name="excelFileBase64" value="@Model.ExcelFileBase64" />
                            <input type="hidden" name="excelFileName" value="@Model.ExcelFileName" />
                            <div class="row align-items-end">
                                <div class="col-auto">
                                    <label for="selectedSheet" class="form-label">Select Sheet:</label>
                                    <select class="form-select" id="selectedSheet" name="selectedSheet" onchange="this.form.submit()">
                                        @foreach (var sheet in Model.SheetNames)
                                        {
                                            <option value="@sheet" selected="@(sheet == Model.SelectedSheet)">@sheet</option>
                                        }
                                    </select>
                                </div>
                            </div>
                        </form>
                    }

                    <div class="mb-3">
                        <button type="button" class="btn btn-success" onclick="saveAsNewFile()">Save As New File</button>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <tbody>
                                @foreach (var row in Model.GridData)
                                {
                                    <tr>
                                        @foreach (var cell in row)
                                        {
                                            <td>
                                                @if (cell.IsEditable)
                                                {
                                                    <div class="editable-cell" 
                                                         data-address="@cell.Address"
                                                         data-sheet="@Model.SelectedSheet"
                                                         data-file="@Model.ExcelFileBase64"
                                                         data-filename="@Model.ExcelFileName">
                                                        <span class="cell-value">@cell.Value</span>
                                                        @if (!string.IsNullOrEmpty(cell.ImageHtml))
                                                        {
                                                            <div class="cell-image mt-1">@Html.Raw(cell.ImageHtml)</div>
                                                        }
                                                    </div>
                                                }
                                                else
                                                {
                                                    <span>@cell.Value</span>
                                                    @if (!string.IsNullOrEmpty(cell.ImageHtml))
                                                    {
                                                        <div class="cell-image mt-1">@Html.Raw(cell.ImageHtml)</div>
                                                    }
                                                }
                                            </td>
                                        }
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                    <p class="text-muted">Showing first 50 rows</p>
                </div>
            }
        </div>
    </div>
</div>

@section Scripts {
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const editableCells = document.querySelectorAll('.editable-cell');

            editableCells.forEach(cell => {
                cell.addEventListener('dblclick', function () {
                    const valueSpan = this.querySelector('.cell-value');
                    const currentValue = valueSpan.textContent;
                    const input = document.createElement('input');
                    input.type = 'text';
                    input.value = currentValue;
                    input.className = 'form-control form-control-sm';

                    valueSpan.style.display = 'none';
                    this.insertBefore(input, valueSpan);
                    input.focus();

                    const address = this.dataset.address;
                    const sheet = this.dataset.sheet;
                    const fileBase64 = this.dataset.file;
                    const fileName = this.dataset.filename;

                    function saveChanges() {
                        const newValue = input.value;
                        fetch('/Home/UpdateCell', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                fileBase64: fileBase64,
                                fileName: fileName,
                                sheetName: sheet,
                                cellAddress: address,
                                newValue: newValue
                            })
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                valueSpan.textContent = newValue;
                                // Update the file base64 in all editable cells
                                document.querySelectorAll('.editable-cell').forEach(cell => {
                                    cell.dataset.file = data.newBase64;
                                });

        function saveAsNewFile() {
            const fileBase64 = document.querySelector('.editable-cell')?.dataset.file;
            const fileName = document.querySelector('.editable-cell')?.dataset.filename;

            if (!fileBase64 || !fileName) {
                alert('No file data available');
                return;
            }

            fetch('/Home/SaveAs', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    fileBase64: fileBase64,
                    fileName: fileName
                })
            })
            .then(response => {
                if (response.ok) {
                    // Convert the response to blob and download
                    return response.blob().then(blob => {
                        const url = window.URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.href = url;
                        a.download = `Modified_${fileName}`;
                        document.body.appendChild(a);
                        a.click();
                        window.URL.revokeObjectURL(url);
                        a.remove();
                    });
                } else {
                    return response.json().then(data => {
                        throw new Error(data.message || 'Failed to save file');
                    });
                }
            })
            .catch(error => {
                alert(error.message || 'Failed to save file');
            });
        }
                            } else {
                                alert('Failed to update cell');
                                valueSpan.textContent = currentValue;
                            }
                        })
                        .catch(() => {
                            alert('Failed to update cell');
                            valueSpan.textContent = currentValue;
                        })
                        .finally(() => {
                            input.remove();
                            valueSpan.style.display = '';
                        });

        function saveAsNewFile() {
            const fileBase64 = document.querySelector('.editable-cell')?.dataset.file;
            const fileName = document.querySelector('.editable-cell')?.dataset.filename;

            if (!fileBase64 || !fileName) {
                alert('No file data available');
                return;
            }

            fetch('/Home/SaveAs', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    fileBase64: fileBase64,
                    fileName: fileName
                })
            })
            .then(response => {
                if (response.ok) {
                    // Convert the response to blob and download
                    return response.blob().then(blob => {
                        const url = window.URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.href = url;
                        a.download = `Modified_${fileName}`;
                        document.body.appendChild(a);
                        a.click();
                        window.URL.revokeObjectURL(url);
                        a.remove();
                    });
                } else {
                    return response.json().then(data => {
                        throw new Error(data.message || 'Failed to save file');
                    });
                }
            })
            .catch(error => {
                alert(error.message || 'Failed to save file');
            });
        }
                    }

                    input.addEventListener('blur', saveChanges);
                    input.addEventListener('keypress', function (e) {
                        if (e.key === 'Enter') {
                            e.preventDefault();
                            saveChanges();
                        }
                    });

        function saveAsNewFile() {
            const fileBase64 = document.querySelector('.editable-cell')?.dataset.file;
            const fileName = document.querySelector('.editable-cell')?.dataset.filename;

            if (!fileBase64 || !fileName) {
                alert('No file data available');
                return;
            }

            fetch('/Home/SaveAs', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    fileBase64: fileBase64,
                    fileName: fileName
                })
            })
            .then(response => {
                if (response.ok) {
                    // Convert the response to blob and download
                    return response.blob().then(blob => {
                        const url = window.URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.href = url;
                        a.download = `Modified_${fileName}`;
                        document.body.appendChild(a);
                        a.click();
                        window.URL.revokeObjectURL(url);
                        a.remove();
                    });
                } else {
                    return response.json().then(data => {
                        throw new Error(data.message || 'Failed to save file');
                    });
                }
            })
            .catch(error => {
                alert(error.message || 'Failed to save file');
            });
        }
                });

        function saveAsNewFile() {
            const fileBase64 = document.querySelector('.editable-cell')?.dataset.file;
            const fileName = document.querySelector('.editable-cell')?.dataset.filename;

            if (!fileBase64 || !fileName) {
                alert('No file data available');
                return;
            }

            fetch('/Home/SaveAs', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    fileBase64: fileBase64,
                    fileName: fileName
                })
            })
            .then(response => {
                if (response.ok) {
                    // Convert the response to blob and download
                    return response.blob().then(blob => {
                        const url = window.URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.href = url;
                        a.download = `Modified_${fileName}`;
                        document.body.appendChild(a);
                        a.click();
                        window.URL.revokeObjectURL(url);
                        a.remove();
                    });
                } else {
                    return response.json().then(data => {
                        throw new Error(data.message || 'Failed to save file');
                    });
                }
            })
            .catch(error => {
                alert(error.message || 'Failed to save file');
            });
        }
            });

        function saveAsNewFile() {
            const fileBase64 = document.querySelector('.editable-cell')?.dataset.file;
            const fileName = document.querySelector('.editable-cell')?.dataset.filename;

            if (!fileBase64 || !fileName) {
                alert('No file data available');
                return;
            }

            fetch('/Home/SaveAs', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    fileBase64: fileBase64,
                    fileName: fileName
                })
            })
            .then(response => {
                if (response.ok) {
                    // Convert the response to blob and download
                    return response.blob().then(blob => {
                        const url = window.URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.href = url;
                        a.download = `Modified_${fileName}`;
                        document.body.appendChild(a);
                        a.click();
                        window.URL.revokeObjectURL(url);
                        a.remove();
                    });
                } else {
                    return response.json().then(data => {
                        throw new Error(data.message || 'Failed to save file');
                    });
                }
            })
            .catch(error => {
                alert(error.message || 'Failed to save file');
            });
        }
        });

        function saveAsNewFile() {
            const fileBase64 = document.querySelector('.editable-cell')?.dataset.file;
            const fileName = document.querySelector('.editable-cell')?.dataset.filename;

            if (!fileBase64 || !fileName) {
                alert('No file data available');
                return;
            }

            fetch('/Home/SaveAs', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    fileBase64: fileBase64,
                    fileName: fileName
                })
            })
            .then(response => {
                if (response.ok) {
                    // Convert the response to blob and download
                    return response.blob().then(blob => {
                        const url = window.URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.href = url;
                        a.download = `Modified_${fileName}`;
                        document.body.appendChild(a);
                        a.click();
                        window.URL.revokeObjectURL(url);
                        a.remove();
                    });
                } else {
                    return response.json().then(data => {
                        throw new Error(data.message || 'Failed to save file');
                    });
                }
            })
            .catch(error => {
                alert(error.message || 'Failed to save file');
            });
        }
    </script>
}
