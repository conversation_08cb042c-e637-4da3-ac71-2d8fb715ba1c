using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using ExcelPreviewApp_WebForms.Services;

namespace ExcelPreviewApp_WebForms.Pages;

public class IndexModel : PageModel
{
    private readonly ILogger<IndexModel> _logger;
    private readonly ExcelService _excelService;

    public IndexModel(ILogger<IndexModel> logger, ExcelService excelService)
    {
        _logger = logger;
        _excelService = excelService;
    }

    [BindProperty]
    public IFormFile? ExcelFile { get; set; }

    [BindProperty]
    public string ExcelFileBase64 { get; set; } = "";

    [BindProperty]
    public string ExcelFileName { get; set; } = "";

    [BindProperty]
    public string SelectedSheet { get; set; } = "";

    public List<string> SheetNames { get; set; } = new List<string>();
    public List<List<CellData>> CellData { get; set; } = new List<List<CellData>>();
    public string ErrorMessage { get; set; } = "";

    [BindProperty]
    public string EditedCellsJson { get; set; } = "";

    public void OnGet()
    {
        // Initialize empty state
        _logger.LogInformation("OnGet called");
    }

    public IActionResult OnPost(string action)
    {
        try
        {
            _logger.LogInformation($"OnPostAsync called with action: {action}");
            _logger.LogInformation($"ExcelFile is null: {ExcelFile == null}");
            if (ExcelFile != null)
            {
                _logger.LogInformation($"ExcelFile length: {ExcelFile.Length}");
                _logger.LogInformation($"ExcelFile name: {ExcelFile.FileName}");
            }

            if (action == "upload" || (string.IsNullOrEmpty(action) && ExcelFile != null && ExcelFile.Length > 0))
            {
                if (ExcelFile == null || ExcelFile.Length == 0)
                {
                    ErrorMessage = "Please select an Excel file.";
                    _logger.LogWarning("No file selected or file is empty");
                    return Page();
                }

                if (!_excelService.ValidateExcelFile(ExcelFile))
                {
                    ErrorMessage = "Invalid file. Please select a valid Excel file (.xlsx or .xls) under 10MB.";
                    return Page();
                }

                var result = _excelService.ProcessExcelFile(ExcelFile);

                ExcelFileBase64 = result.ExcelFileBase64;
                ExcelFileName = result.ExcelFileName;
                SheetNames = result.SheetNames;
                SelectedSheet = result.SelectedSheet;
                CellData = result.CellData;
                ErrorMessage = result.ErrorMessage;
            }
            else if (action == "download")
            {
                if (!string.IsNullOrEmpty(ExcelFileBase64))
                {
                    var fileBytes = Convert.FromBase64String(ExcelFileBase64);
                    return File(fileBytes, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", ExcelFileName);
                }
            }
            else if (action == "save_edited")
            {
                if (!string.IsNullOrEmpty(ExcelFileBase64) && !string.IsNullOrEmpty(EditedCellsJson))
                {
                    var editedFileBytes = _excelService.SaveEditedExcelFile(ExcelFileBase64, ExcelFileName, SelectedSheet, EditedCellsJson);
                    if (editedFileBytes != null)
                    {
                        var editedFileName = Path.GetFileNameWithoutExtension(ExcelFileName) + "_edited" + Path.GetExtension(ExcelFileName);
                        return File(editedFileBytes, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", editedFileName);
                    }
                    else
                    {
                        ErrorMessage = "Failed to save edited file. Please try again.";
                    }
                }
            }
            else
            {
                // Sheet selection
                if (!string.IsNullOrEmpty(ExcelFileBase64))
                {
                    var result = _excelService.ProcessExcelFromBase64(ExcelFileBase64, ExcelFileName, SelectedSheet);

                    SheetNames = result.SheetNames;
                    SelectedSheet = result.SelectedSheet;
                    CellData = result.CellData;
                    ErrorMessage = result.ErrorMessage;
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing Excel file");
            ErrorMessage = "An error occurred while processing the file. Please try again.";
        }

        return Page();
    }

    public string GetCellStyle(CellData cell)
    {
        var styles = new List<string>();

        // Background color - only apply if explicitly set and not white/black
        if (!string.IsNullOrEmpty(cell.BackgroundColor) &&
            cell.BackgroundColor.ToUpper() != "#FFFFFF" &&
            cell.BackgroundColor.ToUpper() != "#000000")
        {
            styles.Add($"background-color: {cell.BackgroundColor}");
        }

        // Borders
        if (!string.IsNullOrEmpty(cell.BorderTop))
            styles.Add($"border-top: {cell.BorderTop}");
        if (!string.IsNullOrEmpty(cell.BorderBottom))
            styles.Add($"border-bottom: {cell.BorderBottom}");
        if (!string.IsNullOrEmpty(cell.BorderLeft))
            styles.Add($"border-left: {cell.BorderLeft}");
        if (!string.IsNullOrEmpty(cell.BorderRight))
            styles.Add($"border-right: {cell.BorderRight}");

        // Text alignment
        if (!string.IsNullOrEmpty(cell.HorizontalAlignment) && cell.HorizontalAlignment != "general")
        {
            var alignment = cell.HorizontalAlignment switch
            {
                "left" => "left",
                "center" => "center",
                "right" => "right",
                "justify" => "justify",
                _ => "left"
            };
            styles.Add($"text-align: {alignment}");
        }

        if (!string.IsNullOrEmpty(cell.VerticalAlignment) && cell.VerticalAlignment != "bottom")
        {
            var vAlignment = cell.VerticalAlignment switch
            {
                "top" => "top",
                "center" => "middle",
                "middle" => "middle",
                "bottom" => "bottom",
                _ => "middle"
            };
            styles.Add($"vertical-align: {vAlignment}");
        }

        // Width and height
        if (cell.ColumnWidth.HasValue && cell.ColumnWidth > 0)
        {
            // Convert Excel column width to pixels (approximate)
            var widthPx = (int)(cell.ColumnWidth.Value * 7);
            styles.Add($"width: {widthPx}px");
        }

        if (cell.RowHeight.HasValue && cell.RowHeight > 0)
        {
            // Convert Excel row height to pixels
            var heightPx = (int)(cell.RowHeight.Value * 1.33);
            styles.Add($"height: {heightPx}px");
        }

        return string.Join("; ", styles);
    }

    public string GetCellValueStyle(CellData cell)
    {
        var styles = new List<string>();

        // Font properties
        if (!string.IsNullOrEmpty(cell.FontFamily))
        {
            styles.Add($"font-family: '{cell.FontFamily}', Calibri, Arial, sans-serif");
        }

        if (cell.FontSize > 0)
        {
            styles.Add($"font-size: {(int)cell.FontSize}px");
        }

        // Font color - only apply if explicitly set and not default black
        if (!string.IsNullOrEmpty(cell.FontColor) &&
            cell.FontColor.ToUpper() != "#000000")
        {
            styles.Add($"color: {cell.FontColor}");
        }

        if (cell.IsBold)
        {
            styles.Add("font-weight: bold");
        }

        if (cell.IsItalic)
        {
            styles.Add("font-style: italic");
        }

        if (cell.IsUnderline)
        {
            styles.Add("text-decoration: underline");
        }

        return string.Join("; ", styles);
    }
}
